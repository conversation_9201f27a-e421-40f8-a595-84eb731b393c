"use strict";
const electron = require("electron");
const path = require("path");
const is = {
  dev: !electron.app.isPackaged
};
const platform = {
  isWindows: process.platform === "win32",
  isMacOS: process.platform === "darwin",
  isLinux: process.platform === "linux"
};
const electronApp = {
  setAppUserModelId(id) {
    if (platform.isWindows)
      electron.app.setAppUserModelId(is.dev ? process.execPath : id);
  },
  setAutoLaunch(auto) {
    if (platform.isLinux)
      return false;
    const isOpenAtLogin = () => {
      return electron.app.getLoginItemSettings().openAtLogin;
    };
    if (isOpenAtLogin() !== auto) {
      electron.app.setLoginItemSettings({
        openAtLogin: auto,
        path: process.execPath
      });
      return isOpenAtLogin() === auto;
    } else {
      return true;
    }
  },
  skipProxy() {
    return electron.session.defaultSession.setProxy({ mode: "direct" });
  }
};
const optimizer = {
  watchWindowShortcuts(window, shortcutOptions) {
    if (!window)
      return;
    const { webContents } = window;
    const { escToCloseWindow = false, zoom = false } = shortcutOptions || {};
    webContents.on("before-input-event", (event, input) => {
      if (input.type === "keyDown") {
        if (!is.dev) {
          if (input.code === "KeyR" && (input.control || input.meta))
            event.preventDefault();
        } else {
          if (input.code === "F12") {
            if (webContents.isDevToolsOpened()) {
              webContents.closeDevTools();
            } else {
              webContents.openDevTools({ mode: "undocked" });
              console.log("Open dev tool...");
            }
          }
        }
        if (escToCloseWindow) {
          if (input.code === "Escape" && input.key !== "Process") {
            window.close();
            event.preventDefault();
          }
        }
        if (!zoom) {
          if (input.code === "Minus" && (input.control || input.meta))
            event.preventDefault();
          if (input.code === "Equal" && input.shift && (input.control || input.meta))
            event.preventDefault();
        }
      }
    });
  },
  registerFramelessWindowIpc() {
    electron.ipcMain.on("win:invoke", (event, action) => {
      const win = electron.BrowserWindow.fromWebContents(event.sender);
      if (win) {
        if (action === "show") {
          win.show();
        } else if (action === "showInactive") {
          win.showInactive();
        } else if (action === "min") {
          win.minimize();
        } else if (action === "max") {
          const isMaximized = win.isMaximized();
          if (isMaximized) {
            win.unmaximize();
          } else {
            win.maximize();
          }
        } else if (action === "close") {
          win.close();
        }
      }
    });
  }
};
class SpotifyService {
  authWindow = null;
  async authenticate() {
    try {
      return {
        success: true,
        user: {
          id: "mock_user",
          display_name: "Mock User",
          email: "<EMAIL>"
        }
      };
    } catch (error) {
      console.error("Authentication error:", error);
      return { success: false };
    }
  }
  async getPlaylists() {
    return [
      {
        id: "1",
        name: "My Playlist 1",
        description: "A great playlist",
        tracks: { total: 25 }
      },
      {
        id: "2",
        name: "My Playlist 2",
        description: "Another great playlist",
        tracks: { total: 15 }
      }
    ];
  }
  async getPlaylistTracks(playlistId) {
    return [
      {
        id: "1",
        name: "Track 1",
        artists: [{ name: "Artist 1" }],
        album: { name: "Album 1" },
        duration_ms: 18e4
      },
      {
        id: "2",
        name: "Track 2",
        artists: [{ name: "Artist 2" }],
        album: { name: "Album 2" },
        duration_ms: 21e4
      }
    ];
  }
  async playTrack(trackUri) {
    console.log("Playing track:", trackUri);
  }
  async pauseTrack() {
    console.log("Pausing track");
  }
  async resumeTrack() {
    console.log("Resuming track");
  }
  async seekTrack(position) {
    console.log("Seeking to position:", position);
  }
  async setVolume(volume) {
    console.log("Setting volume to:", volume);
  }
  async getCurrentState() {
    return {
      is_playing: false,
      progress_ms: 0,
      item: null
    };
  }
  async logout() {
    console.log("Logging out");
  }
}
function registerSpotifyHandlers() {
  const spotifyService = new SpotifyService();
  electron.ipcMain.handle("spotify:authenticate", async () => {
    return await spotifyService.authenticate();
  });
  electron.ipcMain.handle("spotify:getPlaylists", async () => {
    return await spotifyService.getPlaylists();
  });
  electron.ipcMain.handle("spotify:getPlaylistTracks", async (_, playlistId) => {
    return await spotifyService.getPlaylistTracks(playlistId);
  });
  electron.ipcMain.handle("spotify:playTrack", async (_, trackUri) => {
    return await spotifyService.playTrack(trackUri);
  });
  electron.ipcMain.handle("spotify:pauseTrack", async () => {
    return await spotifyService.pauseTrack();
  });
  electron.ipcMain.handle("spotify:resumeTrack", async () => {
    return await spotifyService.resumeTrack();
  });
  electron.ipcMain.handle("spotify:seekTrack", async (_, position) => {
    return await spotifyService.seekTrack(position);
  });
  electron.ipcMain.handle("spotify:setVolume", async (_, volume) => {
    return await spotifyService.setVolume(volume);
  });
  electron.ipcMain.handle("spotify:getCurrentState", async () => {
    return await spotifyService.getCurrentState();
  });
  electron.ipcMain.handle("spotify:logout", async () => {
    return await spotifyService.logout();
  });
}
function createWindow() {
  const mainWindow = new electron.BrowserWindow({
    width: 1200,
    height: 800,
    show: false,
    autoHideMenuBar: true,
    ...process.platform === "linux" ? {} : {},
    webPreferences: {
      preload: path.join(__dirname, "../preload/index.js"),
      sandbox: false,
      contextIsolation: true,
      enableRemoteModule: false,
      nodeIntegration: false
    }
  });
  mainWindow.on("ready-to-show", () => {
    mainWindow.show();
  });
  mainWindow.webContents.setWindowOpenHandler((details) => {
    electron.shell.openExternal(details.url);
    return { action: "deny" };
  });
  if (is.dev && process.env["ELECTRON_RENDERER_URL"]) {
    mainWindow.loadURL(process.env["ELECTRON_RENDERER_URL"]);
  } else {
    mainWindow.loadFile(path.join(__dirname, "../renderer/index.html"));
  }
}
electron.app.whenReady().then(() => {
  electronApp.setAppUserModelId("com.odmustafa.spotify-soulseek");
  electron.app.on("browser-window-created", (_, window) => {
    optimizer.watchWindowShortcuts(window);
  });
  electron.ipcMain.handle("ping", () => "pong");
  registerSpotifyHandlers();
  createWindow();
  electron.app.on("activate", function() {
    if (electron.BrowserWindow.getAllWindows().length === 0) createWindow();
  });
});
electron.app.on("window-all-closed", () => {
  if (process.platform !== "darwin") {
    electron.app.quit();
  }
});
