{"name": "spotify-soulseek-app", "version": "1.0.0", "description": "Desktop app integrating Spotify Web API with Soulseek P2P network", "main": "dist/main/index.js", "author": "<PERSON>", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/odmustafa/spotify-api.git"}, "scripts": {"dev": "electron-vite dev", "build": "electron-vite build", "preview": "electron-vite preview", "build:win": "npm run build && electron-builder --win", "build:mac": "npm run build && electron-builder --mac", "build:linux": "npm run build && electron-builder --linux", "test": "vitest", "test:e2e": "playwright test", "lint": "eslint . --ext .ts,.tsx,.js,.jsx", "lint:fix": "eslint . --ext .ts,.tsx,.js,.jsx --fix", "format": "prettier --write ."}, "devDependencies": {"@electron-toolkit/eslint-config-ts": "^1.0.1", "@electron-toolkit/preload": "^3.0.1", "@electron-toolkit/utils": "^3.0.0", "@playwright/test": "^1.40.0", "@testing-library/jest-dom": "^6.1.4", "@testing-library/react": "^14.1.2", "@types/node": "^20.9.0", "@types/react": "^18.2.37", "@types/react-dom": "^18.2.15", "@types/spotify-web-api-node": "^5.0.11", "@types/sqlite3": "^3.1.11", "@vitejs/plugin-react": "^4.1.1", "electron": "^27.1.3", "electron-builder": "^24.6.4", "electron-vite": "^2.0.0", "eslint": "^8.54.0", "eslint-plugin-react": "^7.33.2", "prettier": "^3.1.0", "react": "^18.2.0", "react-dom": "^18.2.0", "typescript": "^5.2.2", "vite": "^5.0.0", "vitest": "^1.0.0"}, "dependencies": {"@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-progress": "^1.0.3", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-tabs": "^1.0.4", "axios": "^1.6.2", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "electron-store": "^8.1.0", "lucide-react": "^0.294.0", "spotify-web-api-node": "^5.0.2", "sqlite3": "^5.1.6", "tailwind-merge": "^2.0.0", "tailwindcss": "^3.3.6", "tailwindcss-animate": "^1.0.7"}, "build": {"appId": "com.odmustafa.spotify-soulseek", "productName": "Spotify-Soulseek App", "directories": {"output": "dist-electron"}, "files": ["dist/**/*", "python/**/*"], "extraResources": [{"from": "python", "to": "python"}], "win": {"target": "nsis"}, "mac": {"target": "dmg"}, "linux": {"target": "AppImage"}}, "directories": {"doc": "docs"}, "bugs": {"url": "https://github.com/odmustafa/spotify-api/issues"}, "homepage": "https://github.com/odmustafa/spotify-api#readme"}