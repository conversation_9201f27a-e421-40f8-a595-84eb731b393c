import { useState, useEffect } from 'react'
import { Music, Download, Settings, LogIn } from 'lucide-react'

function App(): JSX.Element {
  const [isAuthenticated, setIsAuthenticated] = useState(false)
  const [user, setUser] = useState<any>(null)
  const [loading, setLoading] = useState(false)

  const handleSpotifyLogin = async () => {
    setLoading(true)
    try {
      const result = await window.api.spotify.authenticate()
      if (result.success) {
        setIsAuthenticated(true)
        setUser(result.user)
      }
    } catch (error) {
      console.error('Authentication failed:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleLogout = async () => {
    try {
      await window.api.spotify.logout()
      setIsAuthenticated(false)
      setUser(null)
    } catch (error) {
      console.error('Logout failed:', error)
    }
  }

  if (!isAuthenticated) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-green-400 to-blue-600 flex items-center justify-center p-4">
        <div className="w-full max-w-md bg-white rounded-lg shadow-lg p-6">
          <div className="text-center">
            <div className="mx-auto mb-4 w-16 h-16 bg-green-500 rounded-full flex items-center justify-center">
              <Music className="w-8 h-8 text-white" />
            </div>
            <h1 className="text-2xl font-bold mb-2">Spotify-Soulseek App</h1>
            <p className="text-gray-600 mb-6">
              Connect your Spotify account to get started with music discovery and downloads
            </p>
            <button
              onClick={handleSpotifyLogin}
              disabled={loading}
              className="w-full bg-green-500 hover:bg-green-600 text-white font-medium py-2 px-4 rounded-md flex items-center justify-center disabled:opacity-50"
            >
              <LogIn className="w-4 h-4 mr-2" />
              {loading ? 'Connecting...' : 'Connect with Spotify'}
            </button>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="border-b bg-white shadow-sm">
        <div className="container mx-auto px-4 py-3 flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <Music className="w-6 h-6 text-green-500" />
            <h1 className="text-xl font-bold">Spotify-Soulseek</h1>
          </div>
          <div className="flex items-center space-x-4">
            {user && (
              <span className="text-sm text-gray-600">
                Welcome, {user.display_name}
              </span>
            )}
            <button
              onClick={handleLogout}
              className="px-3 py-1 text-sm border border-gray-300 rounded-md hover:bg-gray-50"
            >
              Logout
            </button>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="container mx-auto px-4 py-6">
        <div className="w-full">
          {/* Tab Navigation */}
          <div className="flex space-x-1 bg-gray-100 p-1 rounded-lg mb-6">
            <button className="flex-1 flex items-center justify-center px-3 py-2 text-sm font-medium rounded-md bg-white shadow-sm">
              <Music className="w-4 h-4 mr-2" />
              Playlists
            </button>
            <button className="flex-1 flex items-center justify-center px-3 py-2 text-sm font-medium rounded-md text-gray-600 hover:text-gray-900">
              <Download className="w-4 h-4 mr-2" />
              Downloads
            </button>
            <button className="flex-1 flex items-center justify-center px-3 py-2 text-sm font-medium rounded-md text-gray-600 hover:text-gray-900">
              <Settings className="w-4 h-4 mr-2" />
              Settings
            </button>
          </div>

          {/* Tab Content */}
          <div className="bg-white rounded-lg shadow-sm border p-6">
            <div>
              <h2 className="text-xl font-semibold mb-2">Your Playlists</h2>
              <p className="text-gray-600 mb-6">
                Browse your Spotify playlists and search for tracks on Soulseek
              </p>
              <div className="text-center py-8 text-gray-500">
                Playlist functionality coming soon...
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  )
}

export default App
