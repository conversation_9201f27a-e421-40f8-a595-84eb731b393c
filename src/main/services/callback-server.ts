import { createServer, Server } from 'http'
import { URL } from 'url'

export class CallbackServer {
  private server: Server | null = null
  private port: number = 8888

  start(): Promise<void> {
    return new Promise((resolve, reject) => {
      this.server = createServer((req, res) => {
        if (req.url?.startsWith('/callback')) {
          // Handle the callback
          res.writeHead(200, { 'Content-Type': 'text/html' })
          res.end(`
            <html>
              <head><title>Spotify Authentication</title></head>
              <body>
                <h1>Authentication Successful!</h1>
                <p>You can now close this window and return to the app.</p>
                <script>
                  setTimeout(() => {
                    window.close();
                  }, 2000);
                </script>
              </body>
            </html>
          `)
        } else {
          res.writeHead(404)
          res.end('Not found')
        }
      })

      this.server.listen(this.port, () => {
        console.log(`Callback server running on port ${this.port}`)
        resolve()
      })

      this.server.on('error', (error) => {
        reject(error)
      })
    })
  }

  stop(): void {
    if (this.server) {
      this.server.close()
      this.server = null
    }
  }

  getCallbackUrl(): string {
    return `http://localhost:${this.port}/callback`
  }
}
